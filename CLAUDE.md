# Memory Management System

A simple, elegant memory management system using Anthropic's Claude API for intelligent memory retrieval.

## Overview

This system stores memories as markdown files with a special naming convention and uses <PERSON> to intelligently search and rank relevant memories based on user prompts.

## Installation

### Option 1: One-Line Install (Recommended)
```bash
curl -sSL https://raw.githubusercontent.com/YOUR_REPO/main/install.sh | bash
```

### Option 2: Manual Setup
```bash
# Clone or download the project
git clone https://github.com/YOUR_REPO/memory-system.git
cd memory-system

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set API key
export ANTHROPIC_API_KEY="your-api-key-here"
```

### Option 3: Single Script Bundle
Download `memory_bundle.py` and run:
```bash
python3 memory_bundle.py
```

## Usage

### Quick Start
```bash
# Run the memory search
python3 run.py

# Or if using the bundle
python3 memory_bundle.py
```

### Adding Memories

Create markdown files in `src/memories/` with this format:
- Filename: `category-project-name-tag-tag.md`
- First line: `INDEX: Brief summary of the memory`

Example:
```markdown
INDEX: Docker best practices for Python applications including multi-stage builds and security.

# Docker Python Best Practices

## Multi-stage Builds
- Use alpine images for smaller size
- Separate build and runtime stages
...
```

### API Functions

```python
from src.app.get_memories import list_memories, get_relevant_memories

# List all memories with their INDEX
memories_index = list_memories()

# Search for relevant memories
import asyncio
memories = asyncio.run(get_relevant_memories("docker optimization"))
```

### Technical Implementation Details

#### Model Configuration
- Uses `claude-3-5-haiku-20241022` for fast memory checking
- Uses `claude-3-5-sonnet-20241022` for intelligent ranking with Extended Thinking mode
- Extended Thinking mode enables deeper analysis for better memory relevance ranking
- Configurable via `CLAUDE_MODEL` and `HAIKU_MODEL` constants in `get_memories.py`

#### Pre-fill Assistant Technique
The ranking function uses Claude's pre-fill feature to ensure consistent list output:
```python
messages=[
    {
        "role": "user", 
        "content": "Rank these memories... Return ONLY a comma-separated list of integers inside square brackets."
    },
    {
        "role": "assistant",
        "content": "["  # Pre-fill ensures response starts with bracket
    }
]
```
This technique improves reliability by constraining the model's output format.

## Delivery Options for Mac Users

### 1. **Shell Script Installer** (Most Elegant)
Create `install.sh`:
```bash
#!/bin/bash
# One-line install: curl -sSL https://example.com/install.sh | bash

echo "🧠 Installing Memory System..."

# Create directory
mkdir -p ~/memories-system
cd ~/memories-system

# Download files
curl -sO https://example.com/memory_bundle.py
curl -sO https://example.com/requirements.txt

# Setup Python environment
python3 -m venv venv
source venv/bin/activate
pip install -q -r requirements.txt

# Create alias
echo 'alias memories="cd ~/memories-system && source venv/bin/activate && python3 memory_bundle.py"' >> ~/.zshrc

echo "✅ Installation complete! Run 'memories' to start."
echo "📝 Don't forget to set ANTHROPIC_API_KEY in your ~/.zshrc"
```

### 2. **Python Bundle** (Single File)
Combine all code into one `memory_bundle.py` that:
- Auto-installs dependencies on first run
- Creates memory directory structure
- Includes all functionality

### 3. **Homebrew Formula** (Most Mac-like)
```ruby
class Memories < Formula
  desc "AI-powered memory management system"
  homepage "https://github.com/YOUR_REPO/memories"
  url "https://github.com/YOUR_REPO/memories/archive/v1.0.0.tar.gz"
  
  depends_on "python@3.11"
  
  def install
    virtualenv_install_with_resources
  end
end
```

### 4. **macOS App** (Simplest for Non-Technical Users)
Use `py2app` to create a .app bundle:
- Double-click to run
- Auto-prompts for API key
- Menu bar integration

## Environment Variables

Set your Anthropic API key:
```bash
export ANTHROPIC_API_KEY="sk-ant-..."
```

## File Structure

```
memory-system/
├── src/
│   ├── memories/       # Memory markdown files
│   │   ├── development-python-api-async-patterns.md
│   │   └── ...
│   └── app/
│       └── get_memories.py
├── tests/             # Test files
│   ├── test_memories.py
│   ├── test_run.py
│   └── test_interactive.py
├── requirements.txt
├── run.py             # Main entry point
└── CLAUDE.md          # This file
```

## Features

- **Parallel Search**: Checks all memory files simultaneously
- **Intelligent Ranking**: Uses Claude to rank memories by relevance
- **Interactive Display**: Choose how many memories to view or summarize
- **Rich Terminal UI**: Beautiful tables and formatting
- **Alphabetical Organization**: Built-in IDE sorting support
- **Extended Thinking**: Uses Claude's Extended Thinking mode for more accurate memory ranking

## Testing

Run the test suite:
```bash
# Activate virtual environment
source venv/bin/activate

# Install test dependencies
pip install pytest pytest-asyncio

# Run all tests
python -m pytest tests/ -v

# Run with output
python -m pytest tests/ -v -s
```

Test files are located in the `tests/` directory:
- `test_memories.py`: Unit tests for memory functions
- `test_run.py`: Integration test for memory search
- `test_interactive.py`: Test for interactive display features