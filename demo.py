#!/usr/bin/env python3
"""Demo script showing all features of the memory system"""

import asyncio
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from src.app.get_memories import list_memories, get_relevant_memories
from rich.console import Console

console = Console()

async def demo():
    console.print("\n[bold blue]🧠 Memory Management System Demo[/bold blue]\n")
    
    # Feature 1: List all memories
    console.print("[yellow]Feature 1: Listing all memories with INDEX[/yellow]")
    list_memories()
    
    # Feature 2: Search for relevant memories
    console.print("\n[yellow]Feature 2: Searching for 'docker' memories[/yellow]")
    memories = await get_relevant_memories("docker")
    
    if memories:
        console.print(f"\nFound {len(memories)} relevant memories:")
        for i, memory in enumerate(memories[:2]):  # Show first 2
            console.print(f"\n[cyan]Memory {i+1}:[/cyan]")
            console.print(memory[:300] + "..." if len(memory) > 300 else memory)
    
    # Feature 3: Another search
    console.print("\n[yellow]Feature 3: Searching for 'machine learning' memories[/yellow]")
    memories = await get_relevant_memories("machine learning")
    
    if memories:
        console.print(f"\nFound {len(memories)} relevant memories:")
        console.print(f"[green]Top result:[/green] {memories[0][:200]}...")
    
    console.print("\n[green]✅ Demo complete![/green]")
    console.print("\nTo run interactively: python3 run.py")

if __name__ == "__main__":
    asyncio.run(demo())