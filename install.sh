#!/bin/bash
# Memory System Installer
# Usage: curl -sSL https://example.com/install.sh | bash

set -e

echo "🧠 Installing Memory Management System..."

# Determine install directory
INSTALL_DIR="$HOME/memory-system"

# Create directory
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# Download the bundle
echo "📥 Downloading memory system..."
curl -sL -o memory_bundle.py https://raw.githubusercontent.com/YOUR_REPO/main/memory_bundle.py
chmod +x memory_bundle.py

# Create a convenient shell function
SHELL_RC="$HOME/.zshrc"
if [ -f "$HOME/.bashrc" ]; then
    SHELL_RC="$HOME/.bashrc"
fi

# Add to shell config if not already present
if ! grep -q "memory-system" "$SHELL_RC"; then
    echo "" >> "$SHELL_RC"
    echo "# Memory Management System" >> "$SHELL_RC"
    echo "alias memories='python3 $INSTALL_DIR/memory_bundle.py'" >> "$SHELL_RC"
    echo "export MEMORY_SYSTEM_DIR='$INSTALL_DIR'" >> "$SHELL_RC"
fi

echo "✅ Installation complete!"
echo ""
echo "📝 Next steps:"
echo "1. Set your Anthropic API key:"
echo "   export ANTHROPIC_API_KEY='your-api-key-here'"
echo ""
echo "2. Reload your shell:"
echo "   source $SHELL_RC"
echo ""
echo "3. Run the memory system:"
echo "   memories"
echo ""
echo "📁 Memories will be stored in: ~/memories-system/memories/"