#!/usr/bin/env python3
"""
Memory Management System - Single File Bundle
This file contains the complete memory system in one portable script.
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path
from typing import List


def ensure_dependencies():
    """Auto-install required dependencies if not present"""
    try:
        import anthropic
        import rich
    except ImportError:
        print("📦 Installing required dependencies...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", 
            "--quiet", "anthropic", "rich"
        ])
        print("✅ Dependencies installed!")


# Install dependencies before imports
ensure_dependencies()

from rich.console import Console
from rich.table import Table
from anthropic import Anthropic


console = Console()
CLAUDE_MODEL = "claude-3-5-sonnet-20241022"  # Latest Claude 3.5 Sonnet
MEMORIES_DIR = Path.home() / "memories-system" / "memories"


def setup_directories():
    """Create memory directory structure if it doesn't exist"""
    MEMORIES_DIR.mkdir(parents=True, exist_ok=True)
    
    # Create sample memory if directory is empty
    if not list(MEMORIES_DIR.glob("*.md")):
        sample = MEMORIES_DIR / "example-memory-system-usage-tips.md"
        sample.write_text(
            "INDEX: Tips for using the memory management system effectively.\n\n"
            "# Memory System Usage\n\n"
            "- Use descriptive filenames with category-project-name-tags format\n"
            "- Keep INDEX lines concise but informative\n"
            "- Organize memories by topic for better retrieval"
        )
        console.print("📝 Created sample memory file", style="green")


def list_memories() -> str:
    """Lists all memory files and their INDEX summary."""
    memories = []
    
    table = Table(title="Memory Index")
    table.add_column("Filename", style="cyan")
    table.add_column("INDEX", style="green")
    
    for file_path in sorted(MEMORIES_DIR.glob("*.md")):
        with open(file_path, 'r') as f:
            lines = f.readlines()
            index_line = lines[0].replace("INDEX: ", "").strip() if lines else "No INDEX found"
        
        filename = file_path.name
        memories.append(f"{filename}: INDEX: {index_line}")
        table.add_row(filename, index_line)
    
    console.print(table)
    return "\n".join(memories)


async def check_file_for_relevant_memories(prompt: str, filepath: Path, memories_index: str, client) -> str:
    """Check a single file for relevant memories using Claude."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    system_prompt = f"""You are a memory retrieval assistant. Given a user prompt and a memory file's content, 
    extract any relevant memories verbatim. If no relevant memories are found, respond with 'no relevant memories'.
    
    Context - All memories index:
    {memories_index}
    
    Current file: {filepath.name}
    """
    
    message = client.messages.create(
        model=CLAUDE_MODEL,
        max_tokens=2000,
        system=system_prompt,
        messages=[{
            "role": "user",
            "content": f"User prompt: {prompt}\n\nMemory file content:\n{content}"
        }]
    )
    
    response = message.content[0].text
    if response.lower() != "no relevant memories":
        return f"From {filepath.name}:\n{response}"
    return "no relevant memories"


def rank_relevant_memories(prompt: str, potentially_relevant_memories: List[str], client) -> List[int]:
    """Rank memories by relevance using Claude."""
    if not potentially_relevant_memories:
        return []
    
    filtered_memories = [m for m in potentially_relevant_memories if m != "no relevant memories"]
    if not filtered_memories:
        return []
    
    memories_text = "\n\n".join([f"Memory {i+1}:\n{mem}" for i, mem in enumerate(filtered_memories)])
    
    message = client.messages.create(
        model=CLAUDE_MODEL,
        max_tokens=100,
        messages=[
            {
                "role": "user", 
                "content": f"""Given this prompt: "{prompt}"
                
                Rank these memories by relevance (most relevant first).
                
                {memories_text}
                
                Return ONLY a comma-separated list of integers representing the ranking inside square brackets.
                For example: "[3,1,2]" means Memory 3 is most relevant, then Memory 1, then Memory 2.
                Do not include any explanation or other text."""
            },
            {
                "role": "assistant",
                "content": "["  # Pre-fill ensures response starts with bracket
            }
        ]
    )
    
    ranking_str = message.content[0].text.strip()
    # Complete the response by adding the opening bracket
    full_response = "[" + ranking_str
    
    # Extract numbers from the bracketed list
    import re
    match = re.search(r'\[?\s*([0-9,\s]+)\s*\]?', full_response)
    if match:
        numbers_str = match.group(1)
        numbers = [int(x.strip()) for x in numbers_str.split(',') if x.strip().isdigit()]
        if numbers:
            return [n - 1 for n in numbers]  # Convert to 0-indexed
    
    # Fallback: return original order
    return list(range(len(filtered_memories)))


async def get_relevant_memories(prompt: str, client) -> List[str]:
    """Orchestrate the two-tier memory retrieval process."""
    memories_index = list_memories()
    
    # Check all files in parallel
    tasks = []
    for filepath in sorted(MEMORIES_DIR.glob("*.md")):
        tasks.append(check_file_for_relevant_memories(prompt, filepath, memories_index, client))
    
    potentially_relevant_memories = await asyncio.gather(*tasks)
    
    # Filter out "no relevant memories" responses
    relevant_memories = [m for m in potentially_relevant_memories if m != "no relevant memories"]
    
    if not relevant_memories:
        return []
    
    # Rank memories
    ranking = rank_relevant_memories(prompt, relevant_memories, client)
    
    # Sort by ranking
    ranked_memories = [relevant_memories[i] for i in ranking]
    
    return ranked_memories


def display_top_n_memories(memories: List[str], prompt: str, client):
    """Interactive display of top N memories with summarization option."""
    if not memories:
        console.print("No relevant memories found.", style="yellow")
        return
    
    total = len(memories)
    n = 10  # default
    
    while True:
        user_input = console.input(f"\nHow many memories would you like to see (1-{total})? Type 's' to summarize (default 10): ")
        
        if user_input.lower() == 's':
            # Summarize memories
            memories_to_summarize = memories[:n]
            memories_text = "\n\n".join(memories_to_summarize)
            
            message = client.messages.create(
                model=CLAUDE_MODEL,
                max_tokens=1000,
                messages=[{
                    "role": "user",
                    "content": f"Original search prompt: '{prompt}'\n\nSummarize these memories as they relate to the original prompt:\n\n{memories_text}"
                }]
            )
            
            console.print("\n[bold]Summary:[/bold]")
            console.print(message.content[0].text)
            break
            
        elif user_input.isdigit():
            n = min(int(user_input), total)
            table = Table(title=f"Top {n} Relevant Memories")
            table.add_column("Rank", style="cyan", width=5)
            table.add_column("Memory", style="green")
            
            for i, memory in enumerate(memories[:n]):
                table.add_row(str(i + 1), memory)
            
            console.print(table)
            break
            
        elif user_input == "":
            # Default to 10
            n = min(10, total)
            table = Table(title=f"Top {n} Relevant Memories")
            table.add_column("Rank", style="cyan", width=5)
            table.add_column("Memory", style="green")
            
            for i, memory in enumerate(memories[:n]):
                table.add_row(str(i + 1), memory)
            
            console.print(table)
            break


async def main():
    """Main function."""
    # Check API key
    api_key = os.getenv("ANTHROPIC_API_KEY")
    if not api_key:
        console.print("❌ Error: ANTHROPIC_API_KEY not set", style="red")
        console.print("💡 Set it with: export ANTHROPIC_API_KEY='your-key'", style="yellow")
        
        # Offer to set it temporarily
        key_input = console.input("\nEnter your API key now (or press Enter to exit): ")
        if key_input:
            api_key = key_input
        else:
            return
    
    client = Anthropic(api_key=api_key)
    
    # Setup directories
    setup_directories()
    
    console.print("\n🧠 Memory Management System", style="bold blue")
    console.print(f"📁 Memories directory: {MEMORIES_DIR}", style="dim")
    
    prompt = console.input("\n🔍 Enter your search prompt: ")
    console.print("\nSearching memories...", style="yellow")
    
    memories = await get_relevant_memories(prompt, client)
    display_top_n_memories(memories, prompt, client)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n👋 Goodbye!", style="green")
    except Exception as e:
        console.print(f"\n❌ Error: {e}", style="red")