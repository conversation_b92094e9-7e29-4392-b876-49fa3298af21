import os
import async<PERSON>
from pathlib import Path
from typing import List, <PERSON><PERSON>
from rich.console import <PERSON>sole
from rich.table import Table
from anthropic import Anthropic

console = Console()
client = Anthropic()

MEMORIES_DIR = Path(__file__).parent.parent / "memories"


def list_memories() -> str:
    """Lists all memory files and their INDEX summary."""
    memories = []
    
    table = Table(title="Memory Index")
    table.add_column("Filename", style="cyan")
    table.add_column("INDEX", style="green")
    
    for file_path in sorted(MEMORIES_DIR.glob("*.md")):
        with open(file_path, 'r') as f:
            lines = f.readlines()
            index_line = lines[0].replace("INDEX: ", "").strip() if lines else "No INDEX found"
        
        filename = file_path.name
        memories.append(f"{filename}: INDEX: {index_line}")
        table.add_row(filename, index_line)
    
    console.print(table)
    return "\n".join(memories)


async def check_file_for_relevant_memories(prompt: str, filepath: Path, memories_index: str) -> str:
    """Check a single file for relevant memories using Sonnet 4."""
    with open(filepath, 'r') as f:
        content = f.read()
    
    system_prompt = f"""You are a memory retrieval assistant. Given a user prompt and a memory file's content, 
    extract any relevant memories verbatim. If no relevant memories are found, respond with 'no relevant memories'.
    
    Context - All memories index:
    {memories_index}
    
    Current file: {filepath.name}
    """
    
    message = client.messages.create(
        model="claude-3-5-sonnet-20241022",
        max_tokens=2000,
        system=system_prompt,
        messages=[{
            "role": "user",
            "content": f"User prompt: {prompt}\n\nMemory file content:\n{content}"
        }]
    )
    
    response = message.content[0].text
    if response.lower() != "no relevant memories":
        return f"From {filepath.name}:\n{response}"
    return "no relevant memories"


def rank_relevant_memories(prompt: str, potentially_relevant_memories: List[str]) -> List[int]:
    """Rank memories by relevance using Sonnet 4 with reasoning."""
    if not potentially_relevant_memories:
        return []
    
    filtered_memories = [m for m in potentially_relevant_memories if m != "no relevant memories"]
    if not filtered_memories:
        return []
    
    memories_text = "\n\n".join([f"Memory {i+1}:\n{mem}" for i, mem in enumerate(filtered_memories)])
    
    message = client.messages.create(
        model="claude-3-5-sonnet-20241022",
        max_tokens=2000,
        messages=[{
            "role": "user", 
            "content": f"""Given this prompt: "{prompt}"
            
            Rank these memories by relevance (most relevant first).
            Think step by step about which memories best address the prompt.
            
            {memories_text}
            
            Return ONLY a comma-separated list of integers representing the ranking.
            For example: "3,1,2" means Memory 3 is most relevant, then Memory 1, then Memory 2."""
        }]
    )
    
    ranking_str = message.content[0].text.strip()
    return [int(x.strip()) - 1 for x in ranking_str.split(",")]


async def get_relevant_memories(prompt: str) -> List[str]:
    """Orchestrate the two-tier memory retrieval process."""
    memories_index = list_memories()
    
    # Check all files in parallel
    tasks = []
    filepaths = []
    for filepath in sorted(MEMORIES_DIR.glob("*.md")):
        filepaths.append(filepath)
        tasks.append(check_file_for_relevant_memories(prompt, filepath, memories_index))
    
    potentially_relevant_memories = await asyncio.gather(*tasks)
    
    # Filter out "no relevant memories" responses
    relevant_memories = [m for m in potentially_relevant_memories if m != "no relevant memories"]
    
    if not relevant_memories:
        return []
    
    # Rank memories
    ranking = rank_relevant_memories(prompt, relevant_memories)
    
    # Sort by ranking
    ranked_memories = [relevant_memories[i] for i in ranking]
    
    return ranked_memories


def display_top_n_memories(memories: List[str]):
    """Interactive display of top N memories with summarization option."""
    if not memories:
        console.print("No relevant memories found.", style="yellow")
        return
    
    total = len(memories)
    n = 10  # default
    
    while True:
        user_input = console.input(f"\nHow many memories would you like to see (1-{total})? Type 's' to summarize (default 10): ")
        
        if user_input.lower() == 's':
            # Summarize memories
            memories_to_summarize = memories[:n]
            memories_text = "\n\n".join(memories_to_summarize)
            
            message = client.messages.create(
                model="claude-3-5-sonnet-20241022",
                max_tokens=1000,
                messages=[{
                    "role": "user",
                    "content": f"Summarize these memories as they relate to the original prompt:\n\n{memories_text}"
                }]
            )
            
            console.print("\n[bold]Summary:[/bold]")
            console.print(message.content[0].text)
            break
            
        elif user_input.isdigit():
            n = min(int(user_input), total)
            table = Table(title=f"Top {n} Relevant Memories")
            table.add_column("Rank", style="cyan", width=5)
            table.add_column("Memory", style="green")
            
            for i, memory in enumerate(memories[:n]):
                table.add_row(str(i + 1), memory)
            
            console.print(table)
            break
            
        elif user_input == "":
            # Default to 10
            n = min(10, total)
            table = Table(title=f"Top {n} Relevant Memories")
            table.add_column("Rank", style="cyan", width=5)
            table.add_column("Memory", style="green")
            
            for i, memory in enumerate(memories[:n]):
                table.add_row(str(i + 1), memory)
            
            console.print(table)
            break


async def main():
    """Main function for testing."""
    prompt = console.input("Enter your search prompt: ")
    console.print("\nSearching memories...", style="yellow")
    
    memories = await get_relevant_memories(prompt)
    display_top_n_memories(memories)


if __name__ == "__main__":
    asyncio.run(main())