INDEX: AWS Lambda optimization strategies for performance, cost, and cold start reduction.

# AWS Lambda Optimization

## Cold Start Reduction
- Use provisioned concurrency for critical functions
- Keep deployment packages small
- Choose appropriate runtime (Python/Node fastest)

## Performance Optimization
- Reuse connections outside handler
- <PERSON>ache frequently accessed data
- Use Lambda layers for dependencies

## Cost Optimization
- Right-size memory allocation
- Use ARM-based Graviton2 processors
- Implement request batching where possible