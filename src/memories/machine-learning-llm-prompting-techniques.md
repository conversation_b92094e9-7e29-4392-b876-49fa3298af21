INDEX: LLM prompting techniques including few-shot learning, chain-of-thought, and prompt engineering best practices.

# LLM Prompting Techniques

## Few-Shot Learning
- Provide 2-3 examples before the actual prompt
- Keep examples diverse but relevant
- Format consistently

## Chain of Thought
- Break complex problems into steps
- Ask model to explain reasoning
- Use "Let's think step by step"

## System Prompts
- Set clear role and context
- Define output format explicitly
- Include constraints and guidelines