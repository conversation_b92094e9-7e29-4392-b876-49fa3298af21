#!/usr/bin/env python3
"""
Unit tests for the memory management system
"""

import unittest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch, MagicMock
import sys

sys.path.insert(0, str(Path(__file__).parent))

from src.app.get_memories import (
    list_memories, 
    check_file_for_relevant_memories,
    rank_relevant_memories,
    get_relevant_memories,
    MEMORIES_DIR
)


class TestMemorySystem(unittest.TestCase):
    
    @classmethod
    def setUpClass(cls):
        """Create a temporary memories directory for testing"""
        cls.temp_dir = tempfile.mkdtemp()
        cls.original_memories_dir = MEMORIES_DIR
        
        # Patch the MEMORIES_DIR for all tests
        cls.patcher = patch('src.app.get_memories.MEMORIES_DIR', Path(cls.temp_dir))
        cls.patcher.start()
        
        # Create test memory files
        test_memories = {
            "dev-python-testing-unittest.md": 
                "INDEX: Python unittest framework basics and best practices.\n\n"
                "# Python Testing\n\nUse setUp and tearDown methods.",
            
            "infra-docker-compose-networking.md": 
                "INDEX: Docker compose networking configuration.\n\n"
                "# Docker Networking\n\nUse bridge networks for isolation.",
            
            "ml-transformers-finetuning-bert.md":
                "INDEX: Fine-tuning BERT models for NLP tasks.\n\n"
                "# BERT Fine-tuning\n\nUse smaller learning rates."
        }
        
        for filename, content in test_memories.items():
            filepath = Path(cls.temp_dir) / filename
            filepath.write_text(content)
    
    @classmethod
    def tearDownClass(cls):
        """Clean up temporary directory"""
        cls.patcher.stop()
        shutil.rmtree(cls.temp_dir)
    
    def test_list_memories(self):
        """Test listing all memories with INDEX"""
        result = list_memories()
        
        # Check that all files are listed
        self.assertIn("dev-python-testing-unittest.md", result)
        self.assertIn("infra-docker-compose-networking.md", result)
        self.assertIn("ml-transformers-finetuning-bert.md", result)
        
        # Check INDEX content
        self.assertIn("Python unittest framework basics", result)
        self.assertIn("Docker compose networking configuration", result)
        self.assertIn("Fine-tuning BERT models", result)
    
    @patch('src.app.get_memories.client')
    def test_check_file_for_relevant_memories(self, mock_client):
        """Test checking a single file for relevant memories"""
        # Mock the API response
        mock_response = MagicMock()
        mock_response.content = [MagicMock(text="Use setUp and tearDown methods.")]
        mock_client.messages.create.return_value = mock_response
        
        filepath = Path(self.temp_dir) / "dev-python-testing-unittest.md"
        
        # Run the async function
        result = asyncio.run(
            check_file_for_relevant_memories(
                "python testing", 
                filepath, 
                "test index"
            )
        )
        
        self.assertIn("dev-python-testing-unittest.md", result)
        self.assertIn("setUp and tearDown", result)
    
    @patch('src.app.get_memories.client')
    def test_check_file_no_relevant_memories(self, mock_client):
        """Test when no relevant memories are found"""
        mock_response = MagicMock()
        mock_response.content = [MagicMock(text="no relevant memories")]
        mock_client.messages.create.return_value = mock_response
        
        filepath = Path(self.temp_dir) / "dev-python-testing-unittest.md"
        
        result = asyncio.run(
            check_file_for_relevant_memories(
                "javascript webpack", 
                filepath, 
                "test index"
            )
        )
        
        self.assertEqual(result, "no relevant memories")
    
    @patch('src.app.get_memories.client')
    def test_rank_relevant_memories(self, mock_client):
        """Test ranking memories by relevance"""
        mock_response = MagicMock()
        mock_response.content = [MagicMock(text="2,1,3")]
        mock_client.messages.create.return_value = mock_response
        
        memories = [
            "Memory 1: Python testing",
            "Memory 2: Docker networking", 
            "Memory 3: BERT models"
        ]
        
        ranking = rank_relevant_memories("docker setup", memories)
        
        self.assertEqual(ranking, [1, 0, 2])  # 0-indexed
    
    def test_rank_relevant_memories_empty(self):
        """Test ranking with no memories"""
        ranking = rank_relevant_memories("test", [])
        self.assertEqual(ranking, [])
    
    @patch('src.app.get_memories.client')
    def test_get_relevant_memories_integration(self, mock_client):
        """Test the full retrieval process"""
        # Mock check_file responses
        check_responses = [
            MagicMock(content=[MagicMock(text="Python testing info")]),
            MagicMock(content=[MagicMock(text="no relevant memories")]),
            MagicMock(content=[MagicMock(text="no relevant memories")])
        ]
        
        # Mock ranking response
        rank_response = MagicMock(content=[MagicMock(text="1")])
        
        mock_client.messages.create.side_effect = [
            *check_responses,
            rank_response
        ]
        
        memories = asyncio.run(get_relevant_memories("python testing"))
        
        self.assertEqual(len(memories), 1)
        self.assertIn("Python testing info", memories[0])


class TestMemoryFileFormat(unittest.TestCase):
    """Test memory file format validation"""
    
    def test_valid_filename_format(self):
        """Test that filenames follow the correct format"""
        valid_names = [
            "category-project-name-tag.md",
            "dev-api-auth-jwt-refresh.md",
            "ml-nlp-bert-finetuning-classification.md"
        ]
        
        for name in valid_names:
            parts = name[:-3].split('-')  # Remove .md and split
            self.assertGreaterEqual(len(parts), 3, 
                f"Filename {name} should have at least 3 parts")
    
    def test_index_line_format(self):
        """Test that INDEX line is properly formatted"""
        content = "INDEX: This is a test summary.\n\n# Content"
        lines = content.split('\n')
        
        self.assertTrue(lines[0].startswith("INDEX: "))
        self.assertGreater(len(lines[0]), 7)  # More than just "INDEX: "


if __name__ == "__main__":
    # Run tests
    unittest.main()