#!/usr/bin/env python3
"""Test interactive display with predefined input"""

import asyncio
import sys
from pathlib import Path
from unittest.mock import patch
import pytest
sys.path.insert(0, str(Path(__file__).parent))

from src.app.get_memories import get_relevant_memories, display_top_n_memories, client

@pytest.mark.asyncio
async def test():
    print("Testing memory search for: 'async python'")
    print("-" * 50)
    
    memories = await get_relevant_memories("async python")
    
    # Test showing 2 memories
    with patch('rich.console.Console.input', return_value='2'):
        display_top_n_memories(memories, "async python")
    
    print("\n" + "-" * 50)
    print("Testing summary function...")
    
    # Test summary
    with patch('rich.console.Console.input', return_value='s'):
        display_top_n_memories(memories, "async python")

if __name__ == "__main__":
    asyncio.run(test())