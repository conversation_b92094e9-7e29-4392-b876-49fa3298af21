#!/usr/bin/env python3
"""Test the memory system with a sample query"""

import asyncio
import sys
from pathlib import Path
import pytest
sys.path.insert(0, str(Path(__file__).parent))

from src.app.get_memories import get_relevant_memories, display_top_n_memories, client

@pytest.mark.asyncio
async def test():
    print("Testing memory search for: 'python optimization'")
    print("-" * 50)
    
    memories = await get_relevant_memories("python optimization")
    
    if memories:
        print(f"\nFound {len(memories)} relevant memories:")
        for i, memory in enumerate(memories[:3]):  # Show top 3
            print(f"\n--- Memory {i+1} ---")
            print(memory[:200] + "..." if len(memory) > 200 else memory)
    else:
        print("No relevant memories found")

if __name__ == "__main__":
    asyncio.run(test())